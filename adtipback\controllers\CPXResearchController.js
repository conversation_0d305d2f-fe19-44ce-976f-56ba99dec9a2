/**
 * CPX Research Controller
 * Handles CPX Research postback endpoints and survey reward processing
 */

const CPXResearchService = require('../services/CPXResearchService');
const WalletService = require('../services/WalletService');

class CPXResearchController {

  /**
   * Handle CPX Research postback
   * @route GET /api/cpx-research/postback
   * @query {Object} - CPX Research postback parameters
   */
  static async handlePostback(req, res) {
    try {
      console.log('[CPXResearchController] Received postback:', {
        query: req.query,
        ip: req.ip || req.connection.remoteAddress,
        headers: req.headers
      });

      // Get client IP (handle various proxy scenarios)
      const clientIP = req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
                      req.headers['x-real-ip'] ||
                      req.connection.remoteAddress ||
                      req.socket.remoteAddress ||
                      req.ip;

      console.log('[CPXResearchController] Client IP:', clientIP);

      // Validate IP whitelist (in production)
      if (process.env.NODE_ENV === 'production') {
        if (!CPXResearchService.isValidCPXIP(clientIP)) {
          console.warn('[CPXResearchController] Invalid IP:', clientIP);
          return res.status(403).json({
            success: false,
            message: 'Forbidden: Invalid IP address'
          });
        }
      }

      // Process the postback
      const result = await CPXResearchService.processPostback(req.query);

      // Return success response (CPX Research expects 200 OK)
      return res.status(200).json({
        success: true,
        message: result.message || 'Postback processed successfully',
        transaction_id: result.transaction_id
      });

    } catch (error) {
      console.error('[CPXResearchController] Error processing postback:', error);
      
      // Return error response but still 200 OK to prevent CPX Research retries
      return res.status(200).json({
        success: false,
        message: error.message || 'Error processing postback'
      });
    }
  }

  /**
   * Credit survey reward (called by frontend)
   * @route POST /api/credit-survey-reward
   * @body {Object} - Survey reward data
   */
  static async creditSurveyReward(req, res) {
    try {
      const {
        userId,
        amount,
        surveyId,
        isPremium,
        source,
        description
      } = req.body;

      console.log('[CPXResearchController] Credit survey reward request:', {
        userId,
        amount,
        surveyId,
        isPremium,
        source
      });

      // Validate required parameters
      if (!userId || !amount || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid userId or amount'
        });
      }

      // Calculate final amount with premium multiplier
      const finalAmount = isPremium ? amount * 4 : amount;

      // Credit user wallet
      const walletResult = await WalletService.creditAdReward(
        parseInt(userId), 
        finalAmount
      );

      if (!walletResult || walletResult.status !== 200) {
        throw new Error('Failed to credit user wallet');
      }

      // Store survey completion record (optional)
      if (surveyId) {
        try {
          await this.storeSurveyCompletion({
            user_id: userId,
            survey_id: surveyId,
            amount: amount,
            final_amount: finalAmount,
            is_premium: isPremium,
            source: source || 'frontend',
            description: description
          });
        } catch (storeError) {
          console.warn('[CPXResearchController] Failed to store survey completion:', storeError);
          // Don't fail the request if storage fails
        }
      }

      console.log('[CPXResearchController] Survey reward credited successfully:', {
        userId,
        amount,
        finalAmount,
        newBalance: walletResult.data?.totalBalance
      });

      return res.status(200).json({
        success: true,
        message: 'Survey reward credited successfully',
        amount_credited: finalAmount,
        new_balance: walletResult.data?.totalBalance,
        availableBalance: walletResult.data?.totalBalance // For compatibility
      });

    } catch (error) {
      console.error('[CPXResearchController] Error crediting survey reward:', error);
      
      return res.status(500).json({
        success: false,
        message: error.message || 'Error crediting survey reward'
      });
    }
  }

  /**
   * Get CPX Research transaction history
   * @route GET /api/cpx-research/transactions
   * @query {Object} - Query parameters (page, limit, userId)
   */
  static async getTransactionHistory(req, res) {
    try {
      const { page = 1, limit = 20, userId } = req.query;
      const requestUserId = userId || req.user?.user_id || req.user?.id;

      if (!requestUserId) {
        return res.status(401).json({
          success: false,
          message: 'User not authenticated'
        });
      }

      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const offset = (pageNum - 1) * limitNum;

      // Get CPX Research transactions
      const query = `
        SELECT 
          id,
          cpx_trans_id,
          offer_id,
          amount_local,
          amount_usd,
          final_amount,
          transaction_type,
          status,
          is_premium,
          created_at,
          updated_at
        FROM cpx_research_transactions 
        WHERE user_id = ? 
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const dbQuery = require('../dbConfig/queryRunner');
      const transactions = await dbQuery.queryRunner(query, [requestUserId, limitNum, offset]);

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM cpx_research_transactions 
        WHERE user_id = ?
      `;
      
      const [countResult] = await dbQuery.queryRunner(countQuery, [requestUserId]);
      const totalRecords = countResult.total;
      const totalPages = Math.ceil(totalRecords / limitNum);

      return res.status(200).json({
        success: true,
        data: transactions,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalRecords,
          hasNextPage: pageNum < totalPages,
          hasPreviousPage: pageNum > 1
        }
      });

    } catch (error) {
      console.error('[CPXResearchController] Error getting transaction history:', error);
      
      return res.status(500).json({
        success: false,
        message: 'Error retrieving transaction history'
      });
    }
  }

  /**
   * Test CPX Research postback (for development)
   * @route POST /api/cpx-research/test-postback
   * @body {Object} - Test postback data
   */
  static async testPostback(req, res) {
    try {
      // Only allow in development
      if (process.env.NODE_ENV === 'production') {
        return res.status(403).json({
          success: false,
          message: 'Test endpoint not available in production'
        });
      }

      const testParams = {
        status: '1',
        trans_id: `test_${Date.now()}`,
        user_id: req.body.user_id || '100',
        amount_local: req.body.amount || '10.00',
        amount_usd: req.body.amount_usd || '0.12',
        offer_id: req.body.offer_id || 'test_offer',
        hash: '', // Will be calculated
        type: 'complete',
        ...req.body
      };

      // Calculate test hash
      const crypto = require('crypto');
      testParams.hash = crypto
        .createHash('md5')
        .update(`${testParams.trans_id}-${CPXResearchService.CPX_APP_SECRET}`)
        .digest('hex');

      console.log('[CPXResearchController] Testing postback with params:', testParams);

      const result = await CPXResearchService.processPostback(testParams);

      return res.status(200).json({
        success: true,
        message: 'Test postback processed successfully',
        result,
        test_params: testParams
      });

    } catch (error) {
      console.error('[CPXResearchController] Error testing postback:', error);
      
      return res.status(500).json({
        success: false,
        message: error.message || 'Error testing postback'
      });
    }
  }

  /**
   * Store survey completion record
   * @param {Object} data - Survey completion data
   * @returns {Promise<number>} - Record ID
   */
  static async storeSurveyCompletion(data) {
    try {
      const query = `
        INSERT INTO survey_completions (
          user_id, survey_id, amount, final_amount, is_premium, 
          source, description, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `;
      
      const values = [
        data.user_id,
        data.survey_id,
        data.amount,
        data.final_amount,
        data.is_premium ? 1 : 0,
        data.source,
        data.description
      ];

      const dbQuery = require('../dbConfig/queryRunner');
      const result = await dbQuery.queryRunner(query, values);
      return result.insertId;
    } catch (error) {
      console.error('[CPXResearchController] Error storing survey completion:', error);
      throw error;
    }
  }
}

module.exports = CPXResearchController;
