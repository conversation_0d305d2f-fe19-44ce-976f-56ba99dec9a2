Postback Settings 
Example: https://www.your-domain.com/file?status={status}&trans_id={trans_id}&user_id={user_id}&sub_id={subid}&sub_id_2={subid_2}&amount_local={amount_local}&amount_usd={amount_usd}&offer_id={offer_ID}&hash={secure_hash}&ip_click={ip_click}

Main Postback URL 
Important: In case a complete will be detected as fraud (usually within 15-60 days), we will call your postback URL again. If the &status={status} parameter is used, the status will change to =2 in the second call. We also recommend to use a unique transaction ID in your postback.
INFORMATION
Please enter a Main Postback URL Postback Url. This entry is mandatory. We call this URL every time a new event is created (e.g. User Complete a Survey, User screened out, User Rate a survey). When you enter a Screen Out Postback in EXPERT SETTINGS, this URL is no longer called for a Screen Out.

You can use the following placeholders:

{status} (1 = completed 2 = canceled) 
{trans_id} (unique ID )
{user_id} (your UserID)
{subid_1} (Your subId1)
{subid_2} (Your subId2)
{amount_local} amount in your currency 
{amount_usd} amount in USD
{ip_click} user click IP
{type} type return out, complete or bonus 
secure_hash} here we had a hash that you can validate the request the hash is a md5 hash: example: md5({trans_id}-yourappsecurehash) 

Postback Whitelist IP: ***********, 2a01:4f8:d0a:30ff::2 and ************

Postback Expert Settings 
Postback Url Screen Out
Postback Bonus/Rating
Postback Url Event Canceled


General (8)
What is the average CPI (Cost per interview) ?
Average payout is around 1.3$ CPI (Cost per interview)

Do I need to have a registered company to use your service?
No, You can use our survey solution to monetize your website/apps without having a registered company.

Do you have any antifraud system?
Yes, we have several Anti-Fraud mechanisms, checking Fingerprints, Cookies, IPs and more. So Fakers don't really have a chance.

What is the suggested commission we have to take?
Every survey is paying a different amount. We recommend to keep 30-50% for yourself. The rate you can set up in your publisher account and also change it any time you want on your own.

Average percentage of chargebacks/reversals ?
Chargebacks are mostly in between 5 - 10% in general. Individually for a publisher, it depends on the quality of survey his userbase is delivering.

How much time does it take to get a chargeback for surveys?
Chargeback are made mostly after 45 to 60 days. But It can be shorter and longer then that depending on partner

Does CPX take Chargebacks/ Reversals?
Yes, we do take back the amount from the reversed surveys.

Where can I see my reversed surveys details?
You can see all the canceled surveys in your publisher account > statistics > Completes > click on the "Filter" button on the top right corner and filter for the status = rejected. You will have the details for all the reversed surveys within the selected period of time.
Payment (4)
What is minimum payout?
The minimum payout is 25 USD for Bank (Wire) and Paypal. For Bitcoin the minimum payout is 100 USD. The payment is done automatically.

I didn't received my payment? / I want to cancel my payment?
We are sorry about that, you can <NAME_EMAIL> with your publisher's email. The team will look into your case and profile feedback. Please make sure that you are adding the Invoice number if it's created already so we are able to find the payment.

When do we need a tax ID?
We need tax ID for only European Publishers

When the payments are made? (Standard)
The invoice for the current month is created on 15 of next month (invoice of January will be created on 15 of February) and then it will be paid in NET30 (30 days after the date of an invoice created).

Postback Settings (11)
Where can I test my postback?
You can test your posback with this button to check if everything is working smoothly.

What type of postback we use?
Server to server postback (GET)

What is a transaction id?
The transaction ID is a unique Id generated everytime a postback is called?

What are Subid_1 and 2 are?
Subid_1 and Subid_2 are extra parameters from us for publisher if he want's to put any extra information in the postback.

What is a md5 hash?
With the md5 hash your postback link can not be manipulated or changed. It's a complete process not very typical but, if you don't understand it's best if you ask a developer to do that for you.

Will both s2s postback and web redirection be fired on the end of a survey?
Our system is waiting about 5-10 seconds maximum after the postback is fired.

What are Amount_local and Amount_usd?
Amount USD is what the publisher gets & Amount local is what the user gets.

Offer Name or Offer ID available?
We do not have an Offername parameter, but we have an Offer ID parameter that can be used in postback: &offer_id={offer_ID}

If postback for reversal is called, will the transaction ID be the same as the first postback?
Yes, we call the same postback with same trans ID with status=2

What is the Expert Settings?
If you use the expert settings:

1. If there is a survey complete, we will call the complete postback URL (Main postback URL)

2. If there is a screen out, we will call the screen out URL, but there are two possibilities:

=> Both of them are called with the (Postback Url Screen Out)
     a) the screen out qualified for the bonus we call the URL with 0.01
     b) the screenout did not qualify for a bonus (we call the URL with 0.00) - that's also to avoid users abusing the bonus system or with very bad quality

3. If the user completed a survey successfully, and he rates that survey after completing it, he gets a 0.01 Bonus and we call Postback Url Bonus

4. If you set up a reconciliation postback (canceled), we call this URL as soon as complete gets reversed. (Postback Url Event Canceled)


Why do I need to setup the postback URL?
 You have to set it up the postback in your publisher account, this is the way we "communicate" the information for the transfer of the reward. So our server will tell you, User 1234 earned XX, and if you receive this postback call you can credit the user.

Reward Settings (3)
What is the currency bonus factor?
We pay a bonus of 0.01 USD for every screen out (user gets redirected to a survey and starts it but does not qualify to some reason) and survey rating (user gives his rating after a successful survey complete). This bonus we pay to you and with the currency bonus factor settings, you can decide what the value is based on the ratio of 1 USD in your local currency. Example: If you use Coins and 1000 Coins have a value of 1 USD on your website, you would set the bonus factor to 1 USD = 1000 (Coins), so that the user gets 10 Coins for 0.01 USD bonus. We always recommend not to take a commission on the bonus and give the bonus reward 100% to the user to keep them motivated.

What is a currency factor?
It's the value of 1 USD in local currency on your page MINUS your commission. For example: if 1 USD on your website is equal to 1000 coins and you want to take 40% commission (so the user earns 60%) you would set the currency factor to 1 USD = 600 coins.

How can I identify a bonus payment?
We actually have this option which you can add to know the "type":

&type={type} - it can be "complete" or "out"

Complete = A successful survey complete, that's quite clear.
Out = If amount_local (or amount_usd) is higher than 0 and type=out, you know it's a screenout with bonus payment (We recommend passing the bonus 100% to the user, to keep them motivated) - The amount for this screenout bonus is 0.01 USD.

Otherwise if amount_local / amount_usd = 0 and type=out, it's a normal screenout with no bonus reward for the user.

Depending on how you integrate/redirect:

There is one more type (type=bonus), this is if a user has a survey complete (which we call one callback for) and if the user gets redirected to us or with our script tag element on your page (where we can display our message_id) or back to our iframe solution, the user has the option to rate the survey and receive another 0.01 USD Bonus for the survey rating which will be called with type=bonus.