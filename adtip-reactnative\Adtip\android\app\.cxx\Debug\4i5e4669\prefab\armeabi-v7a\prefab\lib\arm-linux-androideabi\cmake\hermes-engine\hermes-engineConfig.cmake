if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "F:/R17DevTools/.gradle/caches/8.13/transforms/b6a769c22a825b14c29ec2d96d2172b0/transformed/jetified-hermes-android-0.79.2-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "F:/R17DevTools/.gradle/caches/8.13/transforms/b6a769c22a825b14c29ec2d96d2172b0/transformed/jetified-hermes-android-0.79.2-debug/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

