{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/R17DevTools/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-540a2cfb497010f7918a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-9893f0442356b402d2f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6cdae53d9371c8b6d406.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-9893f0442356b402d2f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-6cdae53d9371c8b6d406.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-540a2cfb497010f7918a.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}