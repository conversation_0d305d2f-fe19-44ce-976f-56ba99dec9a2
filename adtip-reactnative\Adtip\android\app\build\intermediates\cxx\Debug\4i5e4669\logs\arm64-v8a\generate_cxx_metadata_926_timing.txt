# C/C++ build system timings
generate_cxx_metadata
  generate-prefab-packages
    [gap of 143ms]
    exec-prefab 679ms
    [gap of 80ms]
  generate-prefab-packages completed in 902ms
  execute-generate-process
    [gap of 32ms]
    exec-configure 3539ms
    [gap of 502ms]
  execute-generate-process completed in 4073ms
  [gap of 93ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 5113ms

