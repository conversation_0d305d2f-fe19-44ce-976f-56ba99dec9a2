{"buildFiles": ["F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\Debug\\4i5e4669\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["F:\\R17DevTools\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\.cxx\\Debug\\4i5e4669\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_Compressor::@408161e29a6d5b274579": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_Compressor"}, "react_codegen_RNCImageCropPickerSpec::@702b02f8d2524609d414": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCImageCropPickerSpec"}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNDatePickerSpecs::@c00f981c6e74346c63d4": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNDatePickerSpecs"}, "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNDateTimePickerCGen"}, "react_codegen_RNFastImageSpec::@5f53d33017f3c907f455": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNFastImageSpec"}, "react_codegen_RNGoogleMobileAdsSpec::@c00c605517d10bc7886c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNGoogleMobileAdsSpec"}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNImagePickerSpec"}, "react_codegen_RNPermissionsSpec::@7ad697819b753921c957": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNPermissionsSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_lottiereactnative"}, "react_codegen_pagerview::@7032a8921530ec438d60": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_pagerview"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rnblurview::@9a34ffec6e39b5c9049e": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnblurview"}, "react_codegen_rnclipboard::@6385240493dfcaf22ab7": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnclipboard"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnskia::@376d8504f62839611b97": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnskia"}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnsvg", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnviewshot::@0ba03d237e60b9258a87": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnviewshot"}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\intermediates\\cxx\\Debug\\4i5e4669\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\8d81b0c8ae21d76d183ae0c44210c625\\transformed\\jetified-fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "F:\\R17DevTools\\.gradle\\caches\\8.13\\transforms\\70054bad7b567e49310c48ab88316206\\transformed\\jetified-react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}