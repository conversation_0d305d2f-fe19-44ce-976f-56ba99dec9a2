/**
 * Setup CPX Research Tables
 * Creates the necessary database tables for CPX Research integration
 */

const fs = require('fs');
const path = require('path');
const { queryRunner } = require('../dbConfig/queryRunner');

async function setupCPXResearchTables() {
  try {
    console.log('🚀 Setting up CPX Research tables...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../database/cpx_research_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Split SQL statements by semicolon and clean up
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => {
        // Remove empty statements and comments
        if (!stmt || stmt.length === 0) return false;
        if (stmt.startsWith('--')) return false;
        if (stmt.match(/^\s*\/\*/)) return false;
        return true;
      });

    console.log(`📝 Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        // Skip empty statements or comments
        if (!statement || statement.startsWith('--') || statement.startsWith('/*')) {
          continue;
        }

        await queryRunner(statement);
        console.log(`✅ Statement ${i + 1} executed successfully`);
        
      } catch (error) {
        console.error(`❌ Error executing statement ${i + 1}:`, error.message);
        console.log('Statement:', statement.substring(0, 100) + '...');
        
        // Continue with other statements unless it's a critical error
        if (error.message.includes('syntax error') || error.message.includes('unknown column')) {
          throw error;
        }
      }
    }

    console.log('🎉 CPX Research tables setup completed successfully!');

    // Verify tables were created
    await verifyTables();

  } catch (error) {
    console.error('💥 Error setting up CPX Research tables:', error);
    throw error;
  }
}

async function verifyTables() {
  try {
    console.log('🔍 Verifying tables were created...');

    const tables = [
      'cpx_research_transactions',
      'survey_completions',
      'cpx_research_config'
    ];

    for (const table of tables) {
      try {
        const result = await queryRunner(`SHOW TABLES LIKE '${table}'`);
        if (result && result.length > 0) {
          console.log(`✅ Table '${table}' exists`);
          
          // Get table info
          const tableInfo = await queryRunner(`DESCRIBE ${table}`);
          console.log(`   📊 Table '${table}' has ${tableInfo.length} columns`);
        } else {
          console.log(`❌ Table '${table}' not found`);
        }
      } catch (error) {
        console.log(`❌ Error checking table '${table}':`, error.message);
      }
    }

    // Check views
    const views = [
      'cpx_research_summary',
      'cpx_research_daily_stats'
    ];

    for (const view of views) {
      try {
        const result = await queryRunner(`SELECT TABLE_NAME FROM information_schema.VIEWS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = '${view}'`);
        if (result && result.length > 0) {
          console.log(`✅ View '${view}' exists`);
        } else {
          console.log(`❌ View '${view}' not found`);
        }
      } catch (error) {
        console.log(`❌ Error checking view '${view}':`, error.message);
      }
    }

    // Check configuration
    try {
      const config = await queryRunner('SELECT * FROM cpx_research_config LIMIT 1');
      if (config && config.length > 0) {
        console.log('✅ CPX Research configuration found');
        console.log(`   📱 App ID: ${config[0].app_id}`);
        console.log(`   🔄 Active: ${config[0].is_active ? 'Yes' : 'No'}`);
        console.log(`   💰 Premium Multiplier: ${config[0].premium_multiplier}x`);
      } else {
        console.log('❌ CPX Research configuration not found');
      }
    } catch (error) {
      console.log('❌ Error checking configuration:', error.message);
    }

  } catch (error) {
    console.error('💥 Error verifying tables:', error);
  }
}

// Run the setup if this file is executed directly
if (require.main === module) {
  setupCPXResearchTables()
    .then(() => {
      console.log('✨ Setup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = {
  setupCPXResearchTables,
  verifyTables
};
