[{"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IF:/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\F_\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IF:/A1/adtip-reactnative/Adtip/android/app/build/generated/autolinking/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\RNFastImageSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\RNFastImageSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\RNFastImageSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\RNFastImageSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\RNFastImageSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\RNFastImageSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@d11/react-native-fast-image/android/build/generated/source/codegen/jni/react/renderer/components/RNFastImageSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNFastImageSpec_autolinked_build\\CMakeFiles\\react_codegen_RNFastImageSpec.dir\\react\\renderer\\components\\RNFastImageSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@d11\\react-native-fast-image\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNFastImageSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\react\\renderer\\components\\rnclipboard\\rnclipboardJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\rnclipboardJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnclipboard\\rnclipboardJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-clipboard/clipboard/android/build/generated/source/codegen/jni/react/renderer/components/rnclipboard -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnclipboard_autolinked_build\\CMakeFiles\\react_codegen_rnclipboard.dir\\rnclipboard-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\rnclipboard-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-clipboard\\clipboard\\android\\build\\generated\\source\\codegen\\jni\\rnclipboard-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\react\\renderer\\components\\rnblurview\\rnblurviewJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\rnblurviewJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnblurview\\rnblurviewJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/blur/android/build/generated/source/codegen/jni/react/renderer/components/rnblurview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnblurview_autolinked_build\\CMakeFiles\\react_codegen_rnblurview.dir\\rnblurview-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\rnblurview-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\blur\\android\\build\\generated\\source\\codegen\\jni\\rnblurview-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\RNDateTimePickerCGen-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\RNDateTimePickerCGen-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\RNDateTimePickerCGenJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDateTimePickerCGen_autolinked_build\\CMakeFiles\\react_codegen_RNDateTimePickerCGen.dir\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@react-native-community\\datetimepicker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDateTimePickerCGen\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnskia\\rnskiaJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/@shopify/react-native-skia/android/build/generated/source/codegen/jni/react/renderer/components/rnskia -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnskia_autolinked_build\\CMakeFiles\\react_codegen_rnskia.dir\\rnskia-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\rnskia-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\@shopify\\react-native-skia\\android\\build\\generated\\source\\codegen\\jni\\rnskia-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\lottiereactnative-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\lottiereactnative-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\lottiereactnative-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\lottiereactnativeJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\lottiereactnativeJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\lottiereactnativeJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\Compressor-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\Compressor-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\Compressor-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\CompressorJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\CompressorJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\CompressorJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-compressor/android/build/generated/source/codegen/jni/react/renderer/components/Compressor -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o Compressor_autolinked_build\\CMakeFiles\\react_codegen_Compressor.dir\\react\\renderer\\components\\Compressor\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-compressor\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\Compressor\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\RNDatePickerSpecs-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\RNDatePickerSpecs-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\RNDatePickerSpecs-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\RNDatePickerSpecsJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\RNDatePickerSpecsJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\RNDatePickerSpecsJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-date-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNDatePickerSpecs -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNDatePickerSpecs_autolinked_build\\CMakeFiles\\react_codegen_RNDatePickerSpecs.dir\\react\\renderer\\components\\RNDatePickerSpecs\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-date-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNDatePickerSpecs\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\rngesturehandler_codegenJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\RNGoogleMobileAdsSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\RNGoogleMobileAdsSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\RNGoogleMobileAdsSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\2e26a5ddee9cdd09e5c46bc2607ebc87\\RNGoogleMobileAdsSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\RNGoogleMobileAdsSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\RNGoogleMobileAdsSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-google-mobile-ads/android/build/generated/source/codegen/jni/react/renderer/components/RNGoogleMobileAdsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNGoogleMobileAdsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNGoogleMobileAdsSpec.dir\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-google-mobile-ads\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNGoogleMobileAdsSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\RNCImageCropPickerSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\RNCImageCropPickerSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\RNCImageCropPickerSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\react\\renderer\\components\\RNCImageCropPickerSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\react\\renderer\\components\\RNCImageCropPickerSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\react\\renderer\\components\\RNCImageCropPickerSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\c7be1e84762439d6b46bf87a234db436\\RNCImageCropPickerSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\RNCImageCropPickerSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\RNCImageCropPickerSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\react\\renderer\\components\\RNCImageCropPickerSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-crop-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNCImageCropPickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCImageCropPickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCImageCropPickerSpec.dir\\react\\renderer\\components\\RNCImageCropPickerSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-crop-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCImageCropPickerSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\RNImagePickerSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\pagerview-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\pagerview-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o pagerview_autolinked_build\\CMakeFiles\\react_codegen_pagerview.dir\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-pager-view\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\pagerview\\pagerviewJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\RNPermissionsSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\RNPermissionsSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\RNPermissionsSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\RNPermissionsSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\RNPermissionsSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\RNPermissionsSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-permissions/android/build/generated/source/codegen/jni/react/renderer/components/RNPermissionsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNPermissionsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNPermissionsSpec.dir\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-permissions\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNPermissionsSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\dd1b418022f67de1f9ca1d6cf7e4b8e2\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\01c36eb4157acb3ffbcf685718b30a3e\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\f2aae3edee01107646c492c1b81d99d8\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0b4fd8e9908b576d62fe8718f27591d4\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8173d4b68050d9b93d7ac54cf0b4e0ed\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0b4fd8e9908b576d62fe8718f27591d4\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\0b4fd8e9908b576d62fe8718f27591d4\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\46b90de831aab677c1d9d88bd22f23c0\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7b650cfd63d7aa186ed1686240024523\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7893a0707195516e343e2971b9ab58ba\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\143b1fb920479adb1ea4055254169fb0\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\143b1fb920479adb1ea4055254169fb0\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\57cd3c5c209530a13ea61d01e0ecefae\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7893a0707195516e343e2971b9ab58ba\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\57cd3c5c209530a13ea61d01e0ecefae\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\10c544ecff5bceee761d671a2452451e\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\143b1fb920479adb1ea4055254169fb0\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b9b90d27931dbb589e8dd17efe395bba\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9c552b5c7199d6dbb10fef907922ddd7\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\063a6e0f73fe6c2d3a6f1b3a0abec74d\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9c552b5c7199d6dbb10fef907922ddd7\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\063a6e0f73fe6c2d3a6f1b3a0abec74d\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b9b90d27931dbb589e8dd17efe395bba\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\4b2f0bdc16d694d57437385fbe93ae23\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\559af093e8f85ca44b37b2aa4acde804\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\4b2f0bdc16d694d57437385fbe93ae23\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\559af093e8f85ca44b37b2aa4acde804\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\21d411fba7583460414d4fa05403e826\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\5d3231b8f65abb23a4a45dd9d98a3c20\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\a7c92d16ee35ff7717e94fe078008449\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\a7c92d16ee35ff7717e94fe078008449\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\a7c92d16ee35ff7717e94fe078008449\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\5d3231b8f65abb23a4a45dd9d98a3c20\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\States.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\react\\renderer\\components\\rnviewshot\\rnviewshotJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\rnviewshotJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnviewshot\\rnviewshotJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-view-shot/android/build/generated/source/codegen/jni/react/renderer/components/rnviewshot -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnviewshot_autolinked_build\\CMakeFiles\\react_codegen_rnviewshot.dir\\rnviewshot-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\rnviewshot-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-view-shot\\android\\build\\generated\\source\\codegen\\jni\\rnviewshot-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "F:/A1/adtip-reactnative/Adtip/android/app/.cxx/Debug/4i5e4669/arm64-v8a", "command": "F:\\R17DevTools\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=F:/R17DevTools/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -IF:/A1/adtip-reactnative/Adtip/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/8d81b0c8ae21d76d183ae0c44210c625/transformed/jetified-fbjni-0.7.0/prefab/modules/fbjni/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/jsi/include -isystem F:/R17DevTools/.gradle/caches/8.13/transforms/70054bad7b567e49310c48ab88316206/transformed/jetified-react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp", "file": "F:\\A1\\adtip-reactnative\\Adtip\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}]