/**
 * CPX Research Service
 * Handles CPX Research postback processing, validation, and reward management
 */

const crypto = require('crypto');
const dbQuery = require('../dbConfig/queryRunner');
const WalletService = require('./WalletService');

class CPXResearchService {
  
  // CPX Research configuration
  static CPX_APP_ID = '28376';
  static CPX_APP_SECRET = process.env.CPX_RESEARCH_SECRET || 'nihOvt7ttyTmSAgU6TCyXEqT6MLP1ptv'; // Correct secret from CPX dashboard
  
  // CPX Research IP whitelist for security
  static CPX_WHITELIST_IPS = [
    '***********',
    '2a01:4f8:d0a:30ff::2',
    '************'
  ];

  /**
   * Validate if request is from CPX Research servers
   * @param {string} clientIP - Client IP address
   * @returns {boolean} - True if IP is whitelisted
   */
  static isValidCPXIP(clientIP) {
    // Handle IPv6 mapped IPv4 addresses
    const normalizedIP = clientIP.replace(/^::ffff:/, '');
    
    return this.CPX_WHITELIST_IPS.includes(normalizedIP) || 
           this.CPX_WHITELIST_IPS.includes(clientIP);
  }

  /**
   * Validate CPX Research secure hash
   * @param {string} transId - Transaction ID
   * @param {string} providedHash - Hash provided by CPX Research
   * @returns {boolean} - True if hash is valid
   */
  static validateSecureHash(transId, providedHash) {
    if (!transId || !providedHash) {
      return false;
    }

    // Generate expected hash: md5(trans_id-app_secret)
    const expectedHash = crypto
      .createHash('md5')
      .update(`${transId}-${this.CPX_APP_SECRET}`)
      .digest('hex');

    return expectedHash.toLowerCase() === providedHash.toLowerCase();
  }

  /**
   * Process CPX Research postback
   * @param {Object} params - Postback parameters
   * @returns {Promise<Object>} - Processing result
   */
  static async processPostback(params) {
    try {
      const {
        status,
        trans_id,
        user_id,
        amount_local,
        amount_usd,
        offer_id,
        hash,
        ip_click,
        type,
        subid_1,
        subid_2
      } = params;

      console.log('[CPXResearchService] Processing postback:', {
        status,
        trans_id,
        user_id,
        amount_local,
        type
      });

      // Validate required parameters
      if (!status || !trans_id || !user_id || !hash) {
        throw new Error('Missing required parameters');
      }

      // Validate secure hash
      if (!this.validateSecureHash(trans_id, hash)) {
        throw new Error('Invalid secure hash');
      }

      // Check if transaction already processed
      const existingTransaction = await this.getTransactionByTransId(trans_id);
      if (existingTransaction) {
        console.log('[CPXResearchService] Transaction already processed:', trans_id);
        
        // Handle reversal case (status = 2)
        if (status === '2' && existingTransaction.status !== 'reversed') {
          return await this.handleReversal(existingTransaction, params);
        }
        
        return {
          success: true,
          message: 'Transaction already processed',
          transaction_id: existingTransaction.id
        };
      }

      // Process based on status
      if (status === '1') {
        // Survey completed
        return await this.handleSurveyCompletion(params);
      } else if (status === '2') {
        // Survey canceled/fraud - but no existing transaction to reverse
        console.log('[CPXResearchService] Received cancellation for non-existent transaction:', trans_id);
        return {
          success: true,
          message: 'Cancellation received for non-existent transaction'
        };
      } else {
        throw new Error(`Unknown status: ${status}`);
      }

    } catch (error) {
      console.error('[CPXResearchService] Error processing postback:', error);
      throw error;
    }
  }

  /**
   * Handle survey completion
   * @param {Object} params - Postback parameters
   * @returns {Promise<Object>} - Processing result
   */
  static async handleSurveyCompletion(params) {
    const {
      trans_id,
      user_id,
      amount_local,
      amount_usd,
      offer_id,
      type,
      subid_1,
      subid_2
    } = params;

    try {
      // Validate user exists
      const user = await this.getUserById(user_id);
      if (!user) {
        throw new Error(`User not found: ${user_id}`);
      }

      // Calculate reward amount (use amount_local if available, otherwise amount_usd)
      const rewardAmount = parseFloat(amount_local) || parseFloat(amount_usd) || 0;
      
      if (rewardAmount <= 0) {
        throw new Error('Invalid reward amount');
      }

      // Check if user is premium for multiplier
      const isPremium = await this.isUserPremium(user_id);
      const finalAmount = isPremium ? rewardAmount * 4 : rewardAmount;

      // Store transaction record
      const transactionId = await this.storeTransaction({
        trans_id,
        user_id,
        offer_id,
        amount_local: rewardAmount,
        amount_usd: parseFloat(amount_usd) || 0,
        final_amount: finalAmount,
        type: type || 'complete',
        status: 'completed',
        subid_1,
        subid_2,
        is_premium: isPremium
      });

      // Credit user wallet
      const walletResult = await WalletService.creditAdReward(parseInt(user_id), finalAmount);
      
      if (!walletResult || walletResult.status !== 200) {
        throw new Error('Failed to credit user wallet');
      }

      console.log('[CPXResearchService] Survey completion processed successfully:', {
        trans_id,
        user_id,
        rewardAmount,
        finalAmount,
        isPremium
      });

      return {
        success: true,
        message: 'Survey completion processed successfully',
        transaction_id: transactionId,
        amount_credited: finalAmount,
        new_balance: walletResult.data?.totalBalance
      };

    } catch (error) {
      console.error('[CPXResearchService] Error handling survey completion:', error);
      throw error;
    }
  }

  /**
   * Handle survey reversal/chargeback
   * @param {Object} existingTransaction - Existing transaction record
   * @param {Object} params - Postback parameters
   * @returns {Promise<Object>} - Processing result
   */
  static async handleReversal(existingTransaction, params) {
    try {
      const { trans_id, user_id } = params;

      // Mark transaction as reversed
      await this.updateTransactionStatus(existingTransaction.id, 'reversed');

      // Deduct the amount from user's wallet
      const deductAmount = -Math.abs(existingTransaction.final_amount);
      const walletResult = await WalletService.creditAdReward(
        parseInt(user_id), 
        deductAmount
      );

      console.log('[CPXResearchService] Survey reversal processed:', {
        trans_id,
        user_id,
        deducted_amount: Math.abs(deductAmount)
      });

      return {
        success: true,
        message: 'Survey reversal processed successfully',
        transaction_id: existingTransaction.id,
        amount_deducted: Math.abs(deductAmount),
        new_balance: walletResult.data?.totalBalance
      };

    } catch (error) {
      console.error('[CPXResearchService] Error handling reversal:', error);
      throw error;
    }
  }

  /**
   * Get transaction by CPX transaction ID
   * @param {string} transId - CPX transaction ID
   * @returns {Promise<Object|null>} - Transaction record or null
   */
  static async getTransactionByTransId(transId) {
    try {
      const query = `
        SELECT * FROM cpx_research_transactions 
        WHERE cpx_trans_id = ? 
        LIMIT 1
      `;
      
      const [transaction] = await dbQuery.queryRunner(query, [transId]);
      return transaction || null;
    } catch (error) {
      console.error('[CPXResearchService] Error getting transaction:', error);
      return null;
    }
  }

  /**
   * Store CPX Research transaction
   * @param {Object} transactionData - Transaction data
   * @returns {Promise<number>} - Transaction ID
   */
  static async storeTransaction(transactionData) {
    try {
      const query = `
        INSERT INTO cpx_research_transactions (
          cpx_trans_id, user_id, offer_id, amount_local, amount_usd, 
          final_amount, transaction_type, status, subid_1, subid_2, 
          is_premium, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;
      
      const values = [
        transactionData.trans_id,
        transactionData.user_id,
        transactionData.offer_id || null,
        transactionData.amount_local,
        transactionData.amount_usd,
        transactionData.final_amount,
        transactionData.type,
        transactionData.status,
        transactionData.subid_1 || null,
        transactionData.subid_2 || null,
        transactionData.is_premium ? 1 : 0
      ];

      const result = await dbQuery.queryRunner(query, values);
      return result.insertId;
    } catch (error) {
      console.error('[CPXResearchService] Error storing transaction:', error);
      throw error;
    }
  }

  /**
   * Update transaction status
   * @param {number} transactionId - Transaction ID
   * @param {string} status - New status
   * @returns {Promise<void>}
   */
  static async updateTransactionStatus(transactionId, status) {
    try {
      const query = `
        UPDATE cpx_research_transactions 
        SET status = ?, updated_at = NOW() 
        WHERE id = ?
      `;
      
      await dbQuery.queryRunner(query, [status, transactionId]);
    } catch (error) {
      console.error('[CPXResearchService] Error updating transaction status:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {string|number} userId - User ID
   * @returns {Promise<Object|null>} - User record or null
   */
  static async getUserById(userId) {
    try {
      const query = `SELECT * FROM users WHERE id = ? LIMIT 1`;
      const [user] = await dbQuery.queryRunner(query, [userId]);
      return user || null;
    } catch (error) {
      console.error('[CPXResearchService] Error getting user:', error);
      return null;
    }
  }

  /**
   * Check if user is premium
   * @param {string|number} userId - User ID
   * @returns {Promise<boolean>} - True if user is premium
   */
  static async isUserPremium(userId) {
    try {
      const query = `
        SELECT COUNT(*) as count 
        FROM user_premium_subscriptions 
        WHERE user_id = ? 
        AND status = 'active' 
        AND end_date > NOW()
      `;
      
      const [result] = await dbQuery.queryRunner(query, [userId]);
      return result.count > 0;
    } catch (error) {
      console.error('[CPXResearchService] Error checking premium status:', error);
      return false;
    }
  }
}

module.exports = CPXResearchService;
