-- CPX Research Tables
-- Tables for storing CPX Research postback data and survey completions

-- Table for CPX Research transactions (from postbacks)
CREATE TABLE IF NOT EXISTS `cpx_research_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cpx_trans_id` varchar(255) NOT NULL COMMENT 'CPX Research transaction ID',
  `user_id` int(11) NOT NULL COMMENT 'User ID from our system',
  `offer_id` varchar(255) DEFAULT NULL COMMENT 'CPX Research offer/survey ID',
  `amount_local` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Amount in local currency (INR)',
  `amount_usd` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Amount in USD',
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Final amount credited (with premium multiplier)',
  `transaction_type` varchar(50) DEFAULT 'complete' COMMENT 'Type: complete, out, bonus',
  `status` enum('pending','completed','reversed','failed') DEFAULT 'pending' COMMENT 'Transaction status',
  `subid_1` varchar(255) DEFAULT NULL COMMENT 'Custom parameter 1',
  `subid_2` varchar(255) DEFAULT NULL COMMENT 'Custom parameter 2',
  `is_premium` tinyint(1) DEFAULT 0 COMMENT 'Whether user was premium at time of completion',
  `ip_click` varchar(45) DEFAULT NULL COMMENT 'User IP address from CPX Research',
  `postback_data` text DEFAULT NULL COMMENT 'Full postback data as JSON',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_cpx_trans_id` (`cpx_trans_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_cpx_transactions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CPX Research transaction records from postbacks';

-- Table for survey completions (from frontend)
CREATE TABLE IF NOT EXISTS `survey_completions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'User ID',
  `survey_id` varchar(255) NOT NULL COMMENT 'Survey identifier',
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Base reward amount',
  `final_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Final amount with multipliers',
  `is_premium` tinyint(1) DEFAULT 0 COMMENT 'Whether user was premium',
  `source` varchar(50) DEFAULT 'frontend' COMMENT 'Source: frontend, postback, etc.',
  `description` text DEFAULT NULL COMMENT 'Survey description or details',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_survey_id` (`survey_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_survey_completions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Survey completion records from frontend';

-- Table for CPX Research configuration
CREATE TABLE IF NOT EXISTS `cpx_research_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(50) NOT NULL DEFAULT '28376' COMMENT 'CPX Research App ID',
  `app_secret` varchar(255) NOT NULL COMMENT 'CPX Research App Secret Hash',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Whether CPX Research is active',
  `premium_multiplier` decimal(3,1) DEFAULT 4.0 COMMENT 'Premium user reward multiplier',
  `min_payout` decimal(10,2) DEFAULT 0.01 COMMENT 'Minimum payout amount',
  `whitelist_ips` text DEFAULT NULL COMMENT 'JSON array of whitelisted IPs',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CPX Research configuration settings';

-- Insert default configuration
INSERT INTO `cpx_research_config` (
  `app_id`, 
  `app_secret`, 
  `is_active`, 
  `premium_multiplier`, 
  `min_payout`,
  `whitelist_ips`
) VALUES (
  '28376',
  'V3jaWL9UWSXJ6utOhusrpD7F9sFhAclD',
  1,
  4.0,
  0.01,
  '["***********", "2a01:4f8:d0a:30ff::2", "************"]'
) ON DUPLICATE KEY UPDATE
  `updated_at` = CURRENT_TIMESTAMP;

-- Create indexes for better performance
CREATE INDEX `idx_cpx_transactions_user_status` ON `cpx_research_transactions` (`user_id`, `status`);
CREATE INDEX `idx_cpx_transactions_date_status` ON `cpx_research_transactions` (`created_at`, `status`);
CREATE INDEX `idx_survey_completions_user_date` ON `survey_completions` (`user_id`, `created_at`);

-- Add some useful views for reporting
CREATE OR REPLACE VIEW `cpx_research_summary` AS
SELECT 
  u.id as user_id,
  u.username,
  u.phone,
  COUNT(crt.id) as total_surveys,
  SUM(CASE WHEN crt.status = 'completed' THEN crt.final_amount ELSE 0 END) as total_earned,
  SUM(CASE WHEN crt.status = 'reversed' THEN crt.final_amount ELSE 0 END) as total_reversed,
  MAX(crt.created_at) as last_survey_date,
  AVG(CASE WHEN crt.status = 'completed' THEN crt.final_amount ELSE NULL END) as avg_reward
FROM users u
LEFT JOIN cpx_research_transactions crt ON u.id = crt.user_id
GROUP BY u.id, u.username, u.phone;

-- View for daily CPX Research stats
CREATE OR REPLACE VIEW `cpx_research_daily_stats` AS
SELECT 
  DATE(created_at) as survey_date,
  COUNT(*) as total_transactions,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_surveys,
  COUNT(CASE WHEN status = 'reversed' THEN 1 END) as reversed_surveys,
  SUM(CASE WHEN status = 'completed' THEN final_amount ELSE 0 END) as total_paid,
  SUM(CASE WHEN status = 'reversed' THEN final_amount ELSE 0 END) as total_reversed,
  COUNT(DISTINCT user_id) as unique_users,
  AVG(CASE WHEN status = 'completed' THEN final_amount ELSE NULL END) as avg_reward
FROM cpx_research_transactions
GROUP BY DATE(created_at)
ORDER BY survey_date DESC;
